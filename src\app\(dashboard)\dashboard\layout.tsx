import type { Metadata } from "next";
import "../../globals.css";
import { SidebarProvider, SidebarTrigger } from "@/components/ui/sidebar";
import { AppSidebar } from "@/components/dashboard/appSidebarLayout";

export const metadata: Metadata = {
  title: "Create Next App",
  description: "Generated by create next app",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
<div>
    <SidebarProvider className="bg-accent">
      <AppSidebar />
      <div className="w-full py-2 px-2 h-svh">
        <div className="bg-white size-full rounded-lg border-2 ">
          <div className="py-5 px-3 h-1/10">
            <SidebarTrigger className="hover:bg-accent hover:cursor-pointer" />
          </div>
          <hr className="w-full text-accent" />
          <div className="pt-4 px-5 h-9/10 custom classs">{children}</div>
        </div>
      </div>
    </SidebarProvider>
</div>
    
  );
}
