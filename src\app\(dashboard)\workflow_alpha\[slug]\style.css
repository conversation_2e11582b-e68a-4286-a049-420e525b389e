/* xyflow theme files. Delete these to start from our base */

.react-flow {
  /* Custom Variables */
  --xy-theme-selected: #f57dbd;
  --xy-theme-hover: #c5c5c5;
  --xy-theme-edge-hover: #ef4444;
  --xy-theme-color-focus: #e8e8e8;

  /* Built-in Variables see https://reactflow.dev/learn/customization/theming */
  --xy-node-border-default: 3px solid white;
  --xy-node-boxshadow-default: 0px 3.54px 4.55px 0px #00000005,
    0px 3.54px 4.55px 0px #0000000d, 0px 0.51px 1.01px 0px #0000001a;
  --xy-node-border-radius-default: 17px;
  --xy-handle-background-color-default: #ffffff;
  --xy-handle-border-color-default: #aaaaaa;
  --xy-edge-label-color-default: #505050;
}

.react-flow.dark {
  --xy-node-boxshadow-default: 0px 3.54px 4.55px 0px rgba(255, 255, 255, 0.05),
    /* light shadow */ 0px 3.54px 4.55px 0px rgba(255, 255, 255, 0.13),
    /* medium shadow */ 0px 0.51px 1.01px 0px rgba(255, 255, 255, 0.2); /* smallest shadow */
  --xy-theme-color-focus: #535353;
}

/* Customizing Default Theming */

.react-flow__node {
  box-shadow: var(--xy-node-boxshadow-default);
  border-radius: var(--xy-node-border-radius-default);
  background-color: var(--xy-node-background-color-default);
  border: var(--xy-node-border-default);
  color: var(--xy-node-color, var(--xy-node-color-default));
}

.react-flow__node.selectable:focus {
  box-shadow: 0px 0px 0px 4px var(--xy-theme-color-focus);
  border-color: #d9d9d9;
}

.react-flow__node.selectable:focus:active {
  box-shadow: var(--xy-node-boxshadow-default);
}

.react-flow__node.selectable.selected {
  border-color: var(--xy-theme-selected);
  box-shadow: var(--xy-node-boxshadow-default);
}

/* Edge styling and selection feedback */
.react-flow__edge {
  cursor: pointer !important;
  /* Removed transition to prevent edge cutting during drag */
}

.react-flow__edge:hover {
  cursor: pointer !important;
}

.react-flow__edge-path {
  cursor: pointer !important;
  /* Removed transition to prevent edge cutting during drag */
}

.react-flow__edge-path:hover {
  stroke-width: 3px;
  cursor: pointer !important;
}

.react-flow__edge.selected .react-flow__edge-path {
  stroke: #ef4444 !important;
  stroke-width: 3px !important;
}

/* Ensure edges stay connected during node dragging */
.react-flow__edge-updating .react-flow__edge-path {
  stroke: #3b82f6 !important;
  stroke-width: 2px !important;
}

/* Connection line styling */
.react-flow__connection-line {
  stroke: #3b82f6 !important;
  stroke-width: 2px !important;
  stroke-dasharray: none !important;
}

/* Mouse cursor css  */
.react-flow__node {
  cursor: default !important;
}

.react-flow__handle {
  cursor: crosshair !important;
}

.react-flow__selection {
  cursor: default !important;
}

/* Keep default cursor on hover for nodes and handles */
.react-flow__node:hover {
  cursor: default !important;
}

.react-flow__handle:hover {
  cursor: crosshair !important;
}

.react-flow__minimap {
  cursor: default !important;
}

.react-flow__pane.panning {
  cursor: grab !important;
}
.react-flow__pane--panning {
  cursor: grabbing !important;
}

.react-flow__pane {
  cursor: default !important;
}

.react-flow__minimap-mask {
  cursor: default !important;
}