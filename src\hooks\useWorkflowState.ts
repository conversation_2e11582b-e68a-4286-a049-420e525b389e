import { useCallback, useState } from 'react';
import { Node, Edge, useReactFlow } from '@xyflow/react';
import { WorkflowDefinition } from '@/types/workflow';
import { WorkflowIO } from '@/lib/workflow-io';
import { toast } from 'sonner';

export const useWorkflowState = () => {
  const { getNodes, getEdges, setNodes, setEdges, getViewport, setViewport } = useReactFlow();
  const [workflowMetadata, setWorkflowMetadata] = useState({
    id: '',
    name: 'Untitled Workflow',
    description: '',
  });

  const exportWorkflow = useCallback(() => {
    try {
      const nodes = getNodes();
      const edges = getEdges();
      const viewport = getViewport();

      const workflow: WorkflowDefinition = {
        ...workflowMetadata,
        id: workflowMetadata.id || `workflow-${Date.now()}`,
        version: '1.0.0',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        nodes: nodes.map(node => ({
          id: node.id,
          type: node.type || 'default',
          position: node.position,
          data: node.data,
          width: node.width,
          height: node.height,
        })),
        edges: edges.map(edge => ({
          id: edge.id,
          source: edge.source,
          target: edge.target,
          sourceHandle: edge.sourceHandle,
          targetHandle: edge.targetHandle,
          type: edge.type,
          animated: edge.animated,
          style: edge.style,
        })),
        viewport,
      };

      WorkflowIO.exportWorkflow(workflow);
      toast.success('Workflow exported successfully');
    } catch (error) {
      toast.error(error instanceof Error ? error.message : 'Export failed');
    }
  }, [getNodes, getEdges, getViewport, workflowMetadata]);

  const importWorkflow = useCallback(async (file: File) => {
    try {
      const workflow = await WorkflowIO.importWorkflow(file);
      
      // Update metadata
      setWorkflowMetadata({
        id: workflow.id,
        name: workflow.name,
        description: workflow.description || '',
      });

      // Update ReactFlow state
      setNodes(workflow.nodes as Node[]);
      setEdges(workflow.edges as Edge[]);
      
      if (workflow.viewport) {
        setViewport(workflow.viewport);
      }

      toast.success(`Workflow "${workflow.name}" imported successfully`);
      return workflow;
    } catch (error) {
      toast.error(error instanceof Error ? error.message : 'Import failed');
      throw error;
    }
  }, [setNodes, setEdges, setViewport]);

  return {
    workflowMetadata,
    setWorkflowMetadata,
    exportWorkflow,
    importWorkflow,
  };
};
