import { WorkflowDefinition, ImportValidationResult } from '@/types/workflow';

export class WorkflowIO {
  private static readonly CURRENT_VERSION = '1.0.0';
  private static readonly FILE_EXTENSION = '.workflow.json';

  static exportWorkflow(workflow: WorkflowDefinition): void {
    try {
      const exportData = {
        ...workflow,
        version: this.CURRENT_VERSION,
        updatedAt: new Date().toISOString(),
      };

      const jsonString = JSON.stringify(exportData, null, 2);
      const blob = new Blob([jsonString], { type: 'application/json' });
      const url = URL.createObjectURL(blob);
      
      const link = document.createElement('a');
      link.href = url;
      link.download = `${workflow.name || 'workflow'}${this.FILE_EXTENSION}`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(url);
    } catch (error) {
      throw new Error(`Export failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  static async importWorkflow(file: File): Promise<WorkflowDefinition> {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      
      reader.onload = (event) => {
        try {
          const jsonString = event.target?.result as string;
          const workflow = JSON.parse(jsonString) as WorkflowDefinition;
          
          const validation = this.validateWorkflow(workflow);
          if (!validation.isValid) {
            reject(new Error(`Invalid workflow file: ${validation.errors.join(', ')}`));
            return;
          }

          resolve(workflow);
        } catch (error) {
          reject(new Error(`Failed to parse workflow file: ${error instanceof Error ? error.message : 'Invalid JSON'}`));
        }
      };

      reader.onerror = () => reject(new Error('Failed to read file'));
      reader.readAsText(file);
    });
  }

  static validateWorkflow(workflow: any): ImportValidationResult {
    const errors: string[] = [];
    const warnings: string[] = [];

    // Required fields validation
    if (!workflow.id) errors.push('Missing workflow ID');
    if (!workflow.name) errors.push('Missing workflow name');
    if (!workflow.nodes || !Array.isArray(workflow.nodes)) errors.push('Invalid or missing nodes array');
    if (!workflow.edges || !Array.isArray(workflow.edges)) errors.push('Invalid or missing edges array');

    // Node validation
    workflow.nodes?.forEach((node: any, index: number) => {
      if (!node.id) errors.push(`Node ${index}: Missing ID`);
      if (!node.type) errors.push(`Node ${index}: Missing type`);
      if (!node.position || typeof node.position.x !== 'number' || typeof node.position.y !== 'number') {
        errors.push(`Node ${index}: Invalid position`);
      }
    });

    // Edge validation
    workflow.edges?.forEach((edge: any, index: number) => {
      if (!edge.id) errors.push(`Edge ${index}: Missing ID`);
      if (!edge.source) errors.push(`Edge ${index}: Missing source`);
      if (!edge.target) errors.push(`Edge ${index}: Missing target`);
    });

    // Version compatibility check
    if (workflow.version && workflow.version !== this.CURRENT_VERSION) {
      warnings.push(`Workflow version ${workflow.version} may not be fully compatible with current version ${this.CURRENT_VERSION}`);
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings
    };
  }
}
