export interface WorkflowNode {
  id: string;
  type: string;
  position: { x: number; y: number };
  data: {
    value?: any;
    title?: string;
    description?: string;
    apiKey?: string;
    configuration?: Record<string, any>;
    [key: string]: any;
  };
  width?: number;
  height?: number;
}

export interface WorkflowEdge {
  id: string;
  source: string;
  target: string;
  sourceHandle?: string;
  targetHandle?: string;
  type?: string;
  animated?: boolean;
  style?: Record<string, any>;
}

export interface WorkflowDefinition {
  id: string;
  name: string;
  description?: string;
  version: string;
  createdAt: string;
  updatedAt: string;
  nodes: WorkflowNode[];
  edges: WorkflowEdge[];
  viewport?: {
    x: number;
    y: number;
    zoom: number;
  };
  metadata?: {
    tags?: string[];
    category?: string;
    author?: string;
    [key: string]: any;
  };
}

export interface ImportValidationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
}

